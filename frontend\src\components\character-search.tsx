'use client';

import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, X } from 'lucide-react';
import { GetCharactersParams } from '@/types/character';

interface CharacterSearchProps {
  onSearch: (params: GetCharactersParams) => void;
  isLoading?: boolean;
  availableTags?: string[];
}

export function CharacterSearch({ onSearch, isLoading, availableTags = [] }: CharacterSearchProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [storyMode, setStoryMode] = useState<string>('');
  const [showFilters, setShowFilters] = useState(true); // Show by default

  const handleSearch = () => {
    const params: GetCharactersParams = {
      page: 1, // Reset to first page on new search
    };

    if (searchTerm.trim()) {
      params.search = searchTerm.trim();
    }

    if (selectedTags.length > 0) {
      params.tags = selectedTags.join(',');
    }

    if (storyMode) {
      params.storyMode = storyMode;
    }

    onSearch(params);
  };

  const handleTagToggle = (tag: string) => {
    const newSelectedTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag];

    setSelectedTags(newSelectedTags);

    // Auto-search when tags change
    const params: GetCharactersParams = {
      page: 1, // Reset to first page
    };

    if (searchTerm.trim()) {
      params.search = searchTerm.trim();
    }

    if (newSelectedTags.length > 0) {
      params.tags = newSelectedTags.join(',');
    }

    if (storyMode) {
      params.storyMode = storyMode;
    }

    onSearch(params);
  };

  const handleStoryModeChange = (newStoryMode: string) => {
    setStoryMode(newStoryMode);

    // Auto-search when story mode changes
    const params: GetCharactersParams = {
      page: 1, // Reset to first page
    };

    if (searchTerm.trim()) {
      params.search = searchTerm.trim();
    }

    if (selectedTags.length > 0) {
      params.tags = selectedTags.join(',');
    }

    if (newStoryMode) {
      params.storyMode = newStoryMode;
    }

    onSearch(params);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedTags([]);
    setStoryMode('');
    onSearch({ page: 1 });
  };

  const hasActiveFilters = searchTerm || selectedTags.length > 0 || storyMode;

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search characters..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            className="pl-10"
          />
        </div>
        <Button
          onClick={() => setShowFilters(!showFilters)}
          variant="outline"
          size="icon"
          className={showFilters ? "bg-bestieku-primary hover:bg-bestieku-primary-dark" : ""}
        >
          <Filter className="w-4 h-4" />
        </Button>
        <Button
          onClick={handleSearch}
          disabled={isLoading || !searchTerm.trim()}
          className="bg-bestieku-primary hover:bg-bestieku-primary-dark disabled:opacity-50"
          title={!searchTerm.trim() ? 'Enter search term to search' : 'Search characters'}
        >
          {isLoading ? 'Searching...' : 'Search'}
        </Button>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-muted/30 rounded-lg p-4 space-y-4">
          <div className="text-xs text-muted-foreground mb-3">
            💡 Filters apply automatically when selected
          </div>
          {/* Story Mode Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Story Mode</label>
            <div className="flex gap-2">
              <Button
                variant={storyMode === '' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleStoryModeChange('')}
                className={storyMode === '' ? 'bg-bestieku-primary hover:bg-bestieku-primary-dark' : ''}
              >
                All
              </Button>
              <Button
                variant={storyMode === 'true' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleStoryModeChange('true')}
                className={storyMode === 'true' ? 'bg-bestieku-primary hover:bg-bestieku-primary-dark' : ''}
              >
                Story Mode
              </Button>
              <Button
                variant={storyMode === 'false' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleStoryModeChange('false')}
                className={storyMode === 'false' ? 'bg-bestieku-primary hover:bg-bestieku-primary-dark' : ''}
              >
                Regular Chat
              </Button>
            </div>
          </div>

          {/* Tags Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Tags</label>
            {availableTags.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {availableTags.map((tag) => (
                  <Badge
                    key={tag}
                    variant={selectedTags.includes(tag) ? 'default' : 'outline'}
                    className={`cursor-pointer ${
                      selectedTags.includes(tag)
                        ? 'bg-bestieku-primary hover:bg-bestieku-primary-dark'
                        : 'hover:bg-muted'
                    }`}
                    onClick={() => handleTagToggle(tag)}
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No tags available</p>
            )}
          </div>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <div className="flex justify-end">
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="w-4 h-4 mr-1" />
                Clear Filters
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 items-center">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          {searchTerm && (
            <Badge variant="secondary">
              Search: {searchTerm}
            </Badge>
          )}
          {storyMode && (
            <Badge variant="secondary">
              {storyMode === 'true' ? 'Story Mode' : 'Regular Chat'}
            </Badge>
          )}
          {selectedTags.map((tag) => (
            <Badge key={tag} variant="secondary">
              {tag}
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}
