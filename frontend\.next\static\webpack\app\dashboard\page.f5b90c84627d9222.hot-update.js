"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/character-card.tsx":
/*!*******************************************!*\
  !*** ./src/components/character-card.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CharacterCard: () => (/* binding */ CharacterCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ CharacterCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CharacterCard(param) {\n    let { character, onStartChat } = param;\n    _s();\n    const { isAuthenticated } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const handleCardClick = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        console.log('Card clicked:', character.name, 'isAuthenticated:', isAuthenticated);\n        if (isAuthenticated && onStartChat) {\n            console.log('Calling onStartChat with characterId:', character.id);\n            onStartChat(character.id);\n        } else if (!isAuthenticated) {\n            console.log('User not authenticated');\n        } else {\n            console.log('onStartChat function not provided');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-card border rounded-xl p-4 transition-all duration-200 group \".concat(isAuthenticated ? 'hover:shadow-lg hover:scale-[1.02] cursor-pointer hover:border-bestieku-primary/50' : 'hover:shadow-md cursor-default opacity-90'),\n        onClick: handleCardClick,\n        role: isAuthenticated ? \"button\" : undefined,\n        tabIndex: isAuthenticated ? 0 : undefined,\n        onKeyDown: (e)=>{\n            if (isAuthenticated && (e.key === 'Enter' || e.key === ' ')) {\n                e.preventDefault();\n                handleCardClick(e);\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-muted/50 aspect-square rounded-lg overflow-hidden\",\n                        children: character.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: character.image,\n                            alt: character.name,\n                            className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-200\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full flex items-center justify-center text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-12 h-12\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    character.storyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                        variant: \"secondary\",\n                        className: \"absolute top-2 right-2 bg-bestieku-primary text-black\",\n                        children: \"Story Mode\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-lg leading-tight\",\n                        children: character.name\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground line-clamp-2 min-h-[2.5rem]\",\n                        children: character.description\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    character.tags && character.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-1\",\n                        children: [\n                            character.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"text-xs\",\n                                    children: tag\n                                }, tag, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)),\n                            character.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                variant: \"outline\",\n                                className: \"text-xs\",\n                                children: [\n                                    \"+\",\n                                    character.tags.length - 3\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-2 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-xs text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-3 h-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: isAuthenticated ? 'Click to chat' : 'Sign in to chat'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 text-bestieku-primary opacity-0 group-hover:opacity-100 transition-opacity\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium\",\n                                    children: \"Start →\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_s(CharacterCard, \"1LGxUrjNz4q7iKM/2JDC9lJQ3xY=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = CharacterCard;\nvar _c;\n$RefreshReg$(_c, \"CharacterCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/character-card.tsx\n"));

/***/ })

});