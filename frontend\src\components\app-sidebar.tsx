"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import { useTheme } from "next-themes"
import Image from "next/image"
import { useState, useEffect } from "react"
import {
  Castle,
  Heart,
  Home,
  MessageCircle,
  Rocket,
  Settings,
  Sword,
} from "lucide-react"

import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
} from "@/components/ui/sidebar"

const data = {
  navMain: [
    {
      title: "Eksplor",
      url: "/dashboard",
      icon: Home,
      description: "Jelajahi karakter AI",
    },
    {
      title: "Cha<PERSON> Say<PERSON>",
      url: "/chat",
      icon: MessageCircle,
      description: "Lanjutkan percakapan",
    },
    {
      title: "Favorit",
      url: "#",
      icon: Heart,
      description: "Karakter tersimpan",
    },
    {
      title: "Pengaturan",
      url: "/settings",
      icon: Settings,
      description: "Akun & preferensi",
    },
  ],
  quickActions: [
    {
      name: "Fan<PERSON><PERSON>",
      url: "#",
      icon: Castle,
      color: "from-purple-500 to-pink-500",
    },
    {
      name: "<PERSON><PERSON>",
      url: "#",
      icon: Heart,
      color: "from-pink-500 to-rose-500",
    },
    {
      name: "Petualangan",
      url: "#",
      icon: Sword,
      color: "from-orange-500 to-red-500",
    },
    {
      name: "Sci-Fi",
      url: "#",
      icon: Rocket,
      color: "from-blue-500 to-bestieku-secondary",
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname()
  const { theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  return (
    <Sidebar variant="inset" {...props} className="border-r-0">
      <SidebarHeader className="border-b-0 p-4">
        <a href="/dashboard" className="group flex items-center justify-center w-full">
          <Image
            src={mounted && theme === 'dark' ? '/logowhite.png' : '/logoblack.png'}
            alt="Bestieku Logo"
            width={400}
            height={150}
            style={{ width: '280px', height: 'auto' }}
            className="transition-all duration-300 group-hover:scale-105"
            priority
          />
        </a>
      </SidebarHeader>

      <SidebarContent className="px-4">
        {/* Main Navigation */}
        <div className="space-y-2 mb-8">
          <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider px-3 mb-3">
            Navigasi
          </h3>
          {data.navMain.map((item) => {
            const isActive = pathname === item.url
            return (
              <div key={item.title}>
                <a
                  href={item.url}
                  className={`flex items-center gap-3 px-3 h-12 rounded-xl transition-all duration-200 hover:bg-gradient-to-r hover:from-bestieku-primary/10 hover:to-bestieku-primary-dark/10 hover:scale-[1.02] group ${
                    isActive ? 'bg-gradient-to-r from-bestieku-primary/20 to-bestieku-primary-dark/20 border border-bestieku-primary/30' : ''
                  }`}
                >
                  <div className={`p-2 rounded-lg ${
                    isActive
                      ? 'bg-bestieku-primary text-black shadow-md'
                      : 'bg-muted/50 text-muted-foreground group-hover:bg-bestieku-primary/20 group-hover:text-bestieku-primary'
                  } transition-all duration-200`}>
                    <item.icon className="size-4" />
                  </div>
                  <div className="flex-1">
                    <div className={`font-medium ${isActive ? 'text-bestieku-primary' : ''}`}>
                      {item.title}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {item.description}
                    </div>
                  </div>
                </a>
              </div>
            )
          })}
        </div>

        {/* Quick Categories */}
        <div className="space-y-3">
          <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider px-3">
            Kategori Cepat
          </h3>
          <div className="grid grid-cols-2 gap-2">
            {data.quickActions.map((action) => (
              <a
                key={action.name}
                href={action.url}
                className={`p-3 rounded-xl bg-gradient-to-br ${action.color} text-white hover:scale-105 transition-all duration-200 shadow-md hover:shadow-lg group flex flex-col items-center`}
              >
                <action.icon className="w-5 h-5 mb-1" />
                <div className="text-xs font-medium">{action.name}</div>
              </a>
            ))}
          </div>
        </div>
      </SidebarContent>

      <SidebarFooter className="p-4 border-t-0">
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  )
}
