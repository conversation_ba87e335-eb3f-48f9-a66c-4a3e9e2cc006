"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/character-card.tsx":
/*!*******************************************!*\
  !*** ./src/components/character-card.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CharacterCard: () => (/* binding */ CharacterCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ CharacterCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CharacterCard(param) {\n    let { character, onStartChat } = param;\n    _s();\n    const { isAuthenticated } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const handleCardClick = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        console.log('Card clicked:', character.name, 'isAuthenticated:', isAuthenticated);\n        if (isAuthenticated && onStartChat) {\n            console.log('Calling onStartChat with characterId:', character.id);\n            onStartChat(character.id);\n        } else if (!isAuthenticated) {\n            console.log('User not authenticated');\n        } else {\n            console.log('onStartChat function not provided');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-card border rounded-xl p-4 transition-all duration-200 group \".concat(isAuthenticated ? 'hover:shadow-lg hover:scale-[1.02] cursor-pointer hover:border-bestieku-primary/50' : 'hover:shadow-md cursor-default opacity-90'),\n        onClick: handleCardClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-muted/50 aspect-square rounded-lg overflow-hidden\",\n                        children: character.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: character.image,\n                            alt: character.name,\n                            className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-200\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full flex items-center justify-center text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-12 h-12\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    character.storyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                        variant: \"secondary\",\n                        className: \"absolute top-2 right-2 bg-bestieku-primary text-black\",\n                        children: \"Story Mode\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-lg leading-tight\",\n                        children: character.name\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground line-clamp-2 min-h-[2.5rem]\",\n                        children: character.description\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    character.tags && character.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-1\",\n                        children: [\n                            character.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"text-xs\",\n                                    children: tag\n                                }, tag, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this)),\n                            character.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                variant: \"outline\",\n                                className: \"text-xs\",\n                                children: [\n                                    \"+\",\n                                    character.tags.length - 3\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-2 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-xs text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-3 h-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: isAuthenticated ? 'Click to chat' : 'Sign in to chat'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 text-bestieku-primary opacity-0 group-hover:opacity-100 transition-opacity\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium\",\n                                    children: \"Start →\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\character-card.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_s(CharacterCard, \"1LGxUrjNz4q7iKM/2JDC9lJQ3xY=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = CharacterCard;\nvar _c;\n$RefreshReg$(_c, \"CharacterCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/character-card.tsx\n"));

/***/ })

});